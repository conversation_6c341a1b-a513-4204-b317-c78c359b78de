#!/usr/bin/env python3
"""
AthenaCore Codebase Auditing System

This script analyzes the AthenaCore project structure to:
1. Find unused Python files by analyzing imports recursively
2. Generate detailed dependency graphs and reports
3. Safely archive unused files with backups
4. Provide metrics on codebase structure and complexity

Usage:
    python auditcodebase.py [--dry-run] [--visualize] [--detailed] [--force]
    
Options:
    --dry-run    Generate reports without moving files
    --visualize  Generate dependency graph visualization (requires matplotlib)
    --detailed   Generate detailed reports on file metrics and complexity
    --force      Move files even if they're in the safelist
"""

import os
import ast
import sys
import shutil
import logging
import argparse
import datetime
import importlib
import pkgutil
from pathlib import Path
from collections import deque, defaultdict
import re
import json
import time

# Configure logging - file only to avoid interleaved output with print statements
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("audit_log.txt"),
    ]
)
logger = logging.getLogger("AthenaAudit")

# Project constants
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_NAME = os.path.basename(os.path.abspath(ROOT_DIR))
ARCHIVE_DIR = os.path.join(ROOT_DIR, 'archived_files')
BACKUP_DIR = os.path.join(ROOT_DIR, 'audit_backups')
REPORT_DIR = os.path.join(ROOT_DIR, 'audit_reports')
MODULE_MAP_FILE = os.path.join(REPORT_DIR, 'module_map.json')

# Entry points to start dependency tracing from
ENTRY_POINTS = [
    'main.py',                    # Main Flask application entry point
    'src/main.py',                # Alternative main entry point in src directory
    'start_athena.py',            # Startup script for Athena services
    'wsgi.py',                    # WSGI entry point for production
    'cli.py',                     # CLI tools
    'auditcodebase.py',           # This script
]

# Test entry points (separated from production code)
TEST_ENTRY_POINTS = [
    'run_tests.py',               # Test runner
]

# Files that should never be moved (core files)
# Simple filename matches
SAFELIST = [
    'main.py',
    'start_athena.py',
    'wsgi.py',
    'run_tests.py',
    'cli.py',
    'auditcodebase.py',           # Don't move this script itself
    'setup.py',                   # Setup script
    'requirements.txt',           # Dependencies list
    '__init__.py',                # Package initializers
    '.env',                       # Environment variables
    '.env.example',               # Example environment file
    '.gitignore',                 # Git ignore file
    'README.md',                  # Documentation
]

# Regex pattern matches for directories and file paths
SAFELIST_PATTERNS = [
    r'^__init__\.py$',            # Package initialization files
    r'^main\.py$',               # Main entry point
    r'^src/main\.py$',           # Alternative main entry point
    r'^start_athena\.py$',       # Athena startup script
    r'^wsgi\.py$',               # WSGI entry point
    r'^run_tests\.py$',          # Test runner
    r'^cli\.py$',                # CLI tools
    r'^config/.*',               # Configuration files
    r'^setup\.py$',              # Package setup script
    r'.*/settings\.py$',         # Settings files
    r'^auditcodebase\.py$',      # This script
    
    # Critical AthenaCore components
    r'^src/api/.*',              # All API endpoints
    r'^src/controllers/.*',      # All controller files
    r'^src/core/.*',             # All core functionality files
    r'^src/services/.*',         # All service modules
    r'^src/models/.*',           # All model definitions
    r'^src/login/.*',            # User authentication/login
    r'^src/db/.*',               # Database related files
    r'^src/utils/.*',            # Utility modules
    r'^tests/.*',                # Test files
]

# Extensions to consider as code files
CODE_EXTENSIONS = {
    '.py',     # Python source
    '.pyx',    # Cython
    '.pyd',    # Python compiled module
    '.so',     # Shared object
    '.c',      # C source
    '.cpp',    # C++ source
    '.h',      # C/C++ header
}

# Directories to exclude from scanning
EXCLUDE_DIRS = {
    'venv',
    'env',
    '.venv',
    '.env',
    '.git',
    '__pycache__',
    'archived_files',
    'audit_backups',
    'audit_reports',
    'node_modules',
    '.pytest_cache',
}

class CodebaseAuditor:
    """Class to analyze and audit the AthenaCore codebase"""
    
    def __init__(self, args=None):
        """Initialize the auditor with command line arguments"""
        self.args = args or self.parse_args()
        self.start_time = time.time()
        
        # Setup directories
        for directory in [ARCHIVE_DIR, BACKUP_DIR, REPORT_DIR]:
            os.makedirs(directory, exist_ok=True)
        
        # Initialize data structures
        self.all_files = {}            # path -> FileNode
        self.import_graph = {}         # path -> set of imported paths
        self.reverse_graph = {}        # path -> set of files importing it
        self.used_files = set()        # set of paths used in the project
        self.test_files = set()        # files only used for testing
        self.module_map = {}           # module name -> file path
        self.entry_points = []         # list of entry point paths
        
        # File statistics
        self.stats = {
            'total_files': 0,
            'py_files': 0,
            'used_files': 0,
            'unused_files': 0,
            'entry_points': 0,
            'imports': 0,
            'circular_imports': 0,
            'error_files': 0,
        }
    
    def parse_args(self):
        """Parse command line arguments"""
        parser = argparse.ArgumentParser(description='AthenaCore Codebase Audit Tool')
        parser.add_argument('--dry-run', action='store_true', help='Generate reports without moving files')
        parser.add_argument('--visualize', action='store_true', help='Generate dependency visualization')
        parser.add_argument('--detailed', action='store_true', help='Generate detailed code metrics')
        parser.add_argument('--force', action='store_true', help='Move files even if in safelist')
        return parser.parse_args()
    
    def run(self):
        """Run the full audit process"""
        logger.info(f"Starting AthenaCore codebase audit at {datetime.datetime.now()}")
        logger.info(f"Project root: {ROOT_DIR}")
        
        # Create backup before any changes
        self.create_backup()
        
        try:
            # Step 1: Index all files in the project
            self.index_files()
            
            # Step 2: Create module mapping
            self.build_module_map()
            
            # Step 3: Parse imports and build dependency graph
            self.analyze_imports()
            
            # Step 4: Traverse graph from entry points to find used files
            self.find_used_files()
            
            # Step 5: Process test files separately
            self.process_test_files()
            
            # Step 6: Generate reports
            self.generate_reports()
            
            # Step 7: Move unused files if not in dry-run mode
            if not self.args.dry_run:
                self.archive_unused_files()
            
            # Step 8: Generate visualization if requested
            if self.args.visualize:
                self.generate_visualization()
            
            # Print summary
            self.print_summary()
            
        except Exception as e:
            logger.error(f"Audit failed with error: {str(e)}", exc_info=True)
            return 1
        
        return 0

    def create_backup(self):
        """Create a timestamped backup of the project"""
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = os.path.join(BACKUP_DIR, f"{PROJECT_NAME}_backup_{timestamp}")
        logger.info(f"Creating backup at {backup_path}")
        
        # Create directories recursively
        os.makedirs(backup_path, exist_ok=True)
        
        # Copy all Python files (only .py to save space)
        for root, dirs, files in os.walk(ROOT_DIR):
            # Skip excluded directories
            dirs[:] = [d for d in dirs if d not in EXCLUDE_DIRS]
            
            # Get relative path from project root
            rel_path = os.path.relpath(root, ROOT_DIR)
            if rel_path == ".":
                rel_path = ""
            
            # Create corresponding directory in backup
            backup_dir = os.path.join(backup_path, rel_path)
            os.makedirs(backup_dir, exist_ok=True)
            
            # Copy Python files
            for file in files:
                if file.endswith('.py'):
                    src_file = os.path.join(root, file)
                    dst_file = os.path.join(backup_dir, file)
                    shutil.copy2(src_file, dst_file)
        
        logger.info(f"Backup completed: {backup_path}")
    
    def index_files(self):
        """Index all Python files in the project recursively"""
        logger.info("Indexing all Python files in the project...")
        
        class FileNode:
            """Class to store file information"""
            def __init__(self, path, rel_path):
                self.path = path              # Absolute path
                self.rel_path = rel_path      # Path relative to project root
                self.imports = set()          # Files imported by this file
                self.imported_by = set()      # Files that import this file
                self.is_entry_point = False   # Whether this is an entry point
                self.is_used = False          # Whether this file is used
                self.is_init = False          # Whether this is an __init__.py
                self.error = None             # Any error during parsing
                self.module_path = None       # Module path (for imports)
                self.complexity = 0           # Cyclomatic complexity
                self.loc = 0                  # Lines of code
        
        # Walk through all directories recursively
        for root, dirs, files in os.walk(ROOT_DIR):
            # Skip excluded directories
            dirs[:] = [d for d in dirs if d not in EXCLUDE_DIRS]
            
            # Process files in this directory
            for file in files:
                # Only process Python files
                if file.endswith('.py'):
                    abs_path = os.path.join(root, file)
                    rel_path = os.path.relpath(abs_path, ROOT_DIR)
                    
                    # Create file node
                    node = FileNode(abs_path, rel_path)
                    node.is_init = (file == '__init__.py')
                    
                    # Check if this is an entry point
                    if file in ENTRY_POINTS:
                        node.is_entry_point = True
                        self.entry_points.append(abs_path)
                    
                    # Add to files dictionary
                    self.all_files[abs_path] = node
                    self.stats['py_files'] += 1
        
        self.stats['total_files'] = len(self.all_files)
        self.stats['entry_points'] = len(self.entry_points)
        logger.info(f"Found {self.stats['py_files']} Python files in project")
        
    def build_module_map(self):
        """Build mapping between module names and file paths"""
        logger.info("Building module name map...")
        
        # Group files by directory
        dir_files = defaultdict(list)
        for file_path, node in self.all_files.items():
            dir_path = os.path.dirname(file_path)
            dir_files[dir_path].append((file_path, node))
        
        # Process each directory to determine package structure
        for dir_path, files in dir_files.items():
            # Check if this directory is a package (has __init__.py)
            is_package = any(os.path.basename(f[0]) == '__init__.py' for f in files)
            
            # Get relative path from project root
            rel_dir = os.path.relpath(dir_path, ROOT_DIR)
            
            # Build module path based on directory structure
            if rel_dir == ".":
                module_prefix = ""
            else:
                # Convert path separators to dots for module paths
                module_prefix = rel_dir.replace(os.path.sep, '.') + '.'
            
            # Register each file under its module path
            for file_path, node in files:
                filename = os.path.basename(file_path)
                
                # Skip __init__.py for direct imports
                if filename == '__init__.py':
                    # Register the directory itself as a module
                    if rel_dir == ".":
                        module_name = PROJECT_NAME.lower()
                    else:
                        module_name = rel_dir.replace(os.path.sep, '.')
                    
                    self.module_map[module_name] = file_path
                    node.module_path = module_name
                else:
                    # Regular Python file
                    module_name = module_prefix + os.path.splitext(filename)[0]
                    self.module_map[module_name] = file_path
                    node.module_path = module_name
        
        # Save module map to file for reference
        with open(MODULE_MAP_FILE, 'w') as f:
            json_map = {k: os.path.relpath(v, ROOT_DIR) for k, v in self.module_map.items()}
            json.dump(json_map, f, indent=2, sort_keys=True)
        
        logger.info(f"Built module map with {len(self.module_map)} entries")
    
    def analyze_imports(self):
        """Parse each Python file to extract imports and build dependency graph"""
        logger.info("Analyzing imports and building dependency graph...")
        
        # Initialize import graph
        self.import_graph = {file_path: set() for file_path in self.all_files}
        self.reverse_graph = {file_path: set() for file_path in self.all_files}
        
        # Process each file
        for file_path, node in self.all_files.items():
            try:
                # Read file content
                with open(file_path, 'r', encoding='utf-8') as f:
                    try:
                        content = f.read()
                        tree = ast.parse(content, filename=file_path)
                        
                        # Count lines of code (excluding blank lines and comments)
                        node.loc = sum(1 for line in content.split('\n') if line.strip() and not line.strip().startswith('#'))
                        
                        # Find import statements
                        self._process_imports(file_path, tree, node)
                        
                    except Exception as e:
                        node.error = str(e)
                        logger.warning(f"Error parsing {node.rel_path}: {str(e)}")
                        self.stats['error_files'] += 1
            except Exception as e:
                node.error = f"File read error: {str(e)}"
                logger.warning(f"Error reading {node.rel_path}: {str(e)}")
                self.stats['error_files'] += 1
        
        # Update reverse dependencies
        for file_path, imports in self.import_graph.items():
            for imported in imports:
                if imported in self.reverse_graph:
                    self.reverse_graph[imported].add(file_path)
        
        # Check for circular imports
        self._detect_circular_imports()
        
        # Update statistics
        total_imports = sum(len(imports) for imports in self.import_graph.values())
        self.stats['imports'] = total_imports
        
        logger.info(f"Found {total_imports} import relationships between files")
    
    def _process_imports(self, file_path, tree, node):
        """Process import statements from AST"""
        file_dir = os.path.dirname(file_path)
        
        # Helper to resolve relative import to absolute path
        def resolve_relative_import(module_name, level):
            if level == 0:  # Not a relative import
                return None
                
            # Go up 'level' directories from current file
            current_path = file_dir
            for _ in range(level):
                current_path = os.path.dirname(current_path)
                if current_path == ROOT_DIR or not current_path:
                    break
            
            # Build potential module path
            if module_name:
                rel_path = os.path.relpath(current_path, ROOT_DIR)
                if rel_path == ".":
                    rel_path = ""
                    
                full_module = (rel_path.replace(os.path.sep, '.') + '.' + module_name).strip('.')
            else:
                # from . import something
                full_module = os.path.relpath(current_path, ROOT_DIR).replace(os.path.sep, '.')
                
            return full_module
        
        # Process each import statement
        for node_item in ast.walk(tree):
            # Regular imports: import x, import x.y, import x.y as z
            if isinstance(node_item, ast.Import):
                for alias in node_item.names:
                    module_name = alias.name.split('.')[0]  # Get root module name
                    
                    # Find file path for this module
                    if module_name in self.module_map:
                        imported_path = self.module_map[module_name]
                        self.import_graph[file_path].add(imported_path)
                        node.imports.add(imported_path)
                        self.stats['imports'] += 1
            
            # From imports: from x import y, from x.y import z, from . import x
            elif isinstance(node_item, ast.ImportFrom):
                module_name = node_item.module
                level = node_item.level
                
                if level > 0:  # Relative import
                    resolved = resolve_relative_import(module_name, level)
                    if resolved:
                        # Try to find the module
                        if resolved in self.module_map:
                            imported_path = self.module_map[resolved]
                            self.import_graph[file_path].add(imported_path)
                            node.imports.add(imported_path)
                            self.stats['imports'] += 1
                            
                        # Try to find submodules
                        for alias in node_item.names:
                            if module_name:  # from .x import y
                                submod = f"{resolved}.{alias.name}"
                            else:  # from . import x
                                submod = f"{resolved}.{alias.name}" if resolved else alias.name
                                
                            if submod in self.module_map:
                                imported_path = self.module_map[submod]
                                self.import_graph[file_path].add(imported_path)
                                node.imports.add(imported_path)
                                self.stats['imports'] += 1
                else:  # Absolute import
                    # Direct module import
                    if module_name and module_name in self.module_map:
                        imported_path = self.module_map[module_name]
                        self.import_graph[file_path].add(imported_path)
                        node.imports.add(imported_path)
                        self.stats['imports'] += 1
                    
                    # Import from statements
                    for alias in node_item.names:
                        # from x import y
                        if module_name:
                            submod = f"{module_name}.{alias.name}"
                        else:
                            submod = alias.name
                            
                        if submod in self.module_map:
                            imported_path = self.module_map[submod]
                            self.import_graph[file_path].add(imported_path)
                            node.imports.add(imported_path)
                            self.stats['imports'] += 1
    
    def _detect_circular_imports(self):
        """Detect and report circular import dependencies"""
        visited = set()
        path = []
        circular_pairs = set()
        
        def dfs(node):
            visited.add(node)
            path.append(node)
            
            for neighbor in self.import_graph.get(node, set()):
                if neighbor in path:
                    # Found a cycle
                    cycle_start = path.index(neighbor)
                    cycle = path[cycle_start:] + [neighbor]
                    for i in range(len(cycle) - 1):
                        circular_pairs.add((cycle[i], cycle[i+1]))
                elif neighbor not in visited:
                    dfs(neighbor)
            
            path.pop()
        
        # Run DFS from each node to detect cycles
        for node in self.all_files:
            if node not in visited:
                dfs(node)
        
        # Update statistics
        self.stats['circular_imports'] = len(circular_pairs)
        
        # Log circular imports if found
        if circular_pairs:
            logger.warning(f"Found {len(circular_pairs)} circular import relationships")
            with open(os.path.join(REPORT_DIR, 'circular_imports.txt'), 'w') as f:
                for a, b in sorted(circular_pairs):
                    a_rel = os.path.relpath(a, ROOT_DIR)
                    b_rel = os.path.relpath(b, ROOT_DIR)
                    f.write(f"{a_rel} <-> {b_rel}\n")

    def find_used_files(self):
        """Traverse the import graph from entry points to find all used files"""
        logger.info("Identifying used files by traversing import graph from entry points...")
        
        # Start with entry points
        queue = deque(self.entry_points)
        self.used_files = set(self.entry_points)
    
        # Mark entry points as used
        for file_path in self.entry_points:
            if file_path in self.all_files:
                self.all_files[file_path].is_used = True
                logger.info(f"Entry point: {os.path.relpath(file_path, ROOT_DIR)}")
                
        if not self.entry_points:
            logger.warning("No entry points found! Using main.py as default entry point")
            # Try to find main.py in the root directory
            main_py = os.path.join(ROOT_DIR, 'main.py')
            if main_py in self.all_files:
                self.used_files.add(main_py)
                self.all_files[main_py].is_used = True
                queue.append(main_py)
                logger.info(f"Using default entry point: main.py")
        
        # Traverse the import graph using BFS
        while queue:
            current = queue.popleft()
            
            if current not in self.import_graph:
                continue
            
            for imported in self.import_graph[current]:
                if imported not in self.used_files and imported in self.all_files and not self._is_safelist_file(imported):
                    self.used_files.add(imported)
                    self.all_files[imported].is_used = True
                    queue.append(imported)
            
        # Mark special files as used
        for file_path, node in self.all_files.items():
            if self._is_safelist_file(file_path) or node.is_init:
                if file_path not in self.used_files:
                    self.used_files.add(file_path)
                    node.is_used = True
        
        # Update statistics
        self.stats['used_files'] = len(self.used_files)
        self.stats['unused_files'] = len(self.all_files) - len(self.used_files)
        
        logger.info(f"Found {self.stats['used_files']} used files and {self.stats['unused_files']} unused files")

    def process_test_files(self):
        """Identify files that are only used in tests"""
        logger.info("Processing test files...")
        
        # Find test entry points
        test_entry_points = []
        for entry_file in TEST_ENTRY_POINTS:
            abs_path = os.path.join(ROOT_DIR, entry_file)
            if os.path.exists(abs_path):
                test_entry_points.append(abs_path)
                logger.info(f"Found test entry point: {entry_file}")
        
        # Consider all files in tests/ directory as test files
        for file_path in list(self.all_files.keys()):
            rel_path = os.path.relpath(file_path, ROOT_DIR).replace(os.path.sep, '/')
            if rel_path.startswith('tests/') or '/tests/' in rel_path:
                self.test_files.add(file_path)
                # If it's used in production code, keep it in used_files
                # Otherwise, remove it from used_files so it's not considered for production
                if file_path in self.used_files and not self._is_imported_by_production_code(file_path):
                    self.used_files.remove(file_path)
    
        # Trace dependencies from test entry points
        for entry in test_entry_points:
            if entry not in self.all_files:
                continue
            
            queue = deque([entry])
            visited = set([entry])
            
            while queue:
                current = queue.popleft()
                self.test_files.add(current)
                
                if current not in self.import_graph:
                    continue
                
                for imported in self.import_graph[current]:
                    if imported not in visited:
                        visited.add(imported)
                        if imported not in self.used_files and imported in self.all_files and not self._is_safelist_file(imported):
                            self.test_files.add(imported)
                            queue.append(imported)
        
        # Mark special files as used
        for file_path, node in self.all_files.items():
            if self._is_safelist_file(file_path) or node.is_init:
                if file_path not in self.used_files:
                    self.used_files.add(file_path)
                    node.is_used = True
        
        # Update statistics after processing test files
        self.stats['test_files'] = len(self.test_files)
        logger.info(f"Found {self.stats['test_files']} test files")
        
        # Remove test files from used_files count in statistics
        production_used_files = len(self.used_files - self.test_files)
        self.stats['used_production_files'] = production_used_files
        logger.info(f"Found {production_used_files} used production files")
    
    def generate_reports(self):
        """Generate detailed reports of the code audit"""
        logger.info("Generating audit reports...")
        
        # Create report directory if it doesn't exist
        os.makedirs(REPORT_DIR, exist_ok=True)
        
        # Identify test files from the used files
        test_files = set()
        production_files = set()
        
        for file_path in self.used_files:
            rel_path = os.path.relpath(file_path, ROOT_DIR).replace(os.path.sep, '/')
            if rel_path.startswith('tests/') or '/tests/' in rel_path or rel_path == 'run_tests.py':
                test_files.add(file_path)
            else:
                production_files.add(file_path)
        
        # Generate main report with file statistics
        with open(os.path.join(REPORT_DIR, 'audit_summary.txt'), 'w') as f:
            f.write(f"=== AthenaCore Codebase Audit Summary ===\n")
            f.write(f"Generated on: {datetime.datetime.now()}\n\n")
            
            f.write(f"Statistics:\n")
            f.write(f"- Total Python files: {self.stats['py_files']}\n")
            f.write(f"- Used production files: {len(production_files)}\n")
            f.write(f"- Used test files: {len(test_files)}\n")
            f.write(f"- Unused files: {self.stats['unused_files']}\n")
            f.write(f"- Entry points: {self.stats['entry_points']}\n")
            f.write(f"- Import relationships: {self.stats['imports']}\n")
            f.write(f"- Circular imports: {self.stats['circular_imports']}\n")
            f.write(f"- Files with errors: {self.stats['error_files']}\n\n")
            
            f.write(f"Entry Points:\n")
            for entry in self.entry_points:
                f.write(f"- {os.path.relpath(entry, ROOT_DIR)}\n")
        
        # Generate list of production files
        with open(os.path.join(REPORT_DIR, 'used_files.txt'), 'w') as f:
            for file_path in sorted(production_files):
                rel_path = os.path.relpath(file_path, ROOT_DIR)
                f.write(f"{rel_path}\n")
        
        # Generate list of test files
        with open(os.path.join(REPORT_DIR, 'test_files.txt'), 'w') as f:
            for file_path in sorted(test_files):
                rel_path = os.path.relpath(file_path, ROOT_DIR)
                f.write(f"{rel_path}\n")
        
        # Generate list of unused files
        with open(os.path.join(REPORT_DIR, 'unused_files.txt'), 'w') as f:
            for file_path, node in sorted(self.all_files.items()):
                if not node.is_used:
                    rel_path = os.path.relpath(file_path, ROOT_DIR)
                    f.write(f"{rel_path}\n")
        
        # Generate dependency lists for each file
        with open(os.path.join(REPORT_DIR, 'dependencies.txt'), 'w') as f:
            for file_path, imports in sorted(self.import_graph.items()):
                if imports:
                    rel_path = os.path.relpath(file_path, ROOT_DIR)
                    f.write(f"\n{rel_path} imports:\n")
                    for imported in sorted(imports):
                        imp_rel_path = os.path.relpath(imported, ROOT_DIR)
                        f.write(f"  - {imp_rel_path}\n")
        
        # Generate files that will be moved
        unused_no_safelist = []
        for file_path, node in self.all_files.items():
            if not node.is_used and os.path.basename(file_path) not in SAFELIST:
                unused_no_safelist.append(file_path)
        
        with open(os.path.join(REPORT_DIR, 'files_to_archive.txt'), 'w') as f:
            for file_path in sorted(unused_no_safelist):
                rel_path = os.path.relpath(file_path, ROOT_DIR)
                f.write(f"{rel_path}\n")
                
        # If detailed flag is set, generate more comprehensive metrics
        if self.args.detailed:
            self._generate_detailed_metrics()
            
        logger.info(f"Reports saved to {REPORT_DIR}")
    
    def _generate_detailed_metrics(self):
        """Generate detailed code metrics if requested"""
        try:
            # Calculate code complexity metrics if available
            try:
                import radon.complexity as cc
                has_radon = True
            except ImportError:
                has_radon = False
                logger.warning("Radon module not found, skipping complexity metrics")
                
            if has_radon:
                with open(os.path.join(REPORT_DIR, 'complexity_metrics.txt'), 'w') as f:
                    f.write(f"=== Code Complexity Metrics ===\n\n")
                    for file_path, node in sorted(self.all_files.items()):
                        try:
                            with open(file_path, 'r', encoding='utf-8') as code_file:
                                content = code_file.read()
                                
                            rel_path = os.path.relpath(file_path, ROOT_DIR)
                            results = cc.cc_visit(content)
                            if results:
                                f.write(f"\n{rel_path}:\n")
                                total_complexity = sum(result.complexity for result in results)
                                f.write(f"  Total Complexity: {total_complexity}\n")
                                f.write(f"  Average Complexity: {total_complexity / len(results):.2f}\n")
                                f.write(f"  Functions: {len(results)}\n")
                                
                                # Individual function complexities
                                for result in sorted(results, key=lambda r: r.complexity, reverse=True):
                                    f.write(f"  - {result.name}: {result.complexity} {cc.rank(result.complexity)}\n")
                        except Exception as e:
                            f.write(f"\n{rel_path}: Error calculating complexity - {str(e)}\n")
            
            # Add AthenaCore specific checks
            if self.args.check_deprecated:
                self._check_deprecated_imports()
                
            if self.args.kb_focus:
                self._analyze_kb_components()
                
            # Generate module dependency visualization
            with open(os.path.join(REPORT_DIR, 'dependency_stats.json'), 'w') as f:
                data = {
                    'files': len(self.all_files),
                    'used_files': len(self.used_files),
                    'unused_files': len(self.all_files) - len(self.used_files),
                    'import_count': self.stats['imports'],
                    'circular_imports': self.stats['circular_imports'],
                    'most_depended_on': [],
                    'most_dependencies': [],
                    'athena_specific': {
                        'kb_components': self._count_special_files('src/core/kb_'),
                        'document_processor': self._count_special_files('document_processor'),
                        'deprecated_imports': 0  # Will be populated if --check-deprecated is used
                    }
                }
                
                # Find files with most imports
                file_import_counts = [(file_path, len(imports)) for file_path, imports in self.import_graph.items()]
                for file_path, count in sorted(file_import_counts, key=lambda x: x[1], reverse=True)[:10]:
                    if count > 0:
                        data['most_dependencies'].append({
                            'file': os.path.relpath(file_path, ROOT_DIR),
                            'count': count
                        })
                
                # Find files imported by most other files
                file_imported_by_counts = [(file_path, len(importers)) for file_path, importers in self.reverse_graph.items()]
                for file_path, count in sorted(file_imported_by_counts, key=lambda x: x[1], reverse=True)[:10]:
                    if count > 0:
                        data['most_depended_on'].append({
                            'file': os.path.relpath(file_path, ROOT_DIR),
                            'count': count
                        })
                
                json.dump(data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error generating detailed metrics: {str(e)}")
    
    def _is_imported_by_production_code(self, file_path):
        """Check if a file is imported by any production code files"""
        # If the file isn't in the reverse_graph, nothing imports it
        if file_path not in self.reverse_graph:
            return False
            
        # Check each file that imports this one
        for importing_file in self.reverse_graph.get(file_path, set()):
            # Get relative path to check if it's a test file
            rel_path = os.path.relpath(importing_file, ROOT_DIR).replace(os.path.sep, '/')
            
            # If the importing file is not a test file, then this file is used by production code
            if not rel_path.startswith('tests/') and '/tests/' not in rel_path and rel_path != 'run_tests.py':
                return True
                
        return False
        
    def _is_safelist_file(self, file_path):
        """Check if a file is in the safelist (should not be moved)"""
        rel_path = os.path.relpath(file_path, ROOT_DIR).replace(os.path.sep, '/')
        filename = os.path.basename(file_path)
        
        # Check simple filename match
        if filename in SAFELIST:
            return True
            
        # Check regex patterns
        for pattern in SAFELIST_PATTERNS:
            if re.match(pattern, rel_path):
                return True
        
        # Special case for critical AthenaCore components based on memory
        critical_components = [
            'kb_catalog.py',
            'document_processor.py',
            'api_v1.py',
            'attachments.py', 
            'command_processor.py',
            'kb_search.py',
            'vector_db.py',
            'task_executor.py',
            'task_scheduler.py',
            'athena_mcp.py',
            'connection.py'
        ]
        
        if filename in critical_components:
            return True
            
        # Protect all API, controllers, core, and MCP directories
        critical_dirs = ['src/api/', 'src/controllers/', 'src/core/', 'src/models/', 'src/services/', 'src/mcp/']
        for dir_path in critical_dirs:
            if dir_path in rel_path:
                return True
                
        return False
        
    def archive_unused_files(self):
        """Move unused files to archive directory"""
        logger.info("Archiving unused files...")
        
        # Create archive directory
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        archive_path = os.path.join(ARCHIVE_DIR, f"unused_files_{timestamp}")
        os.makedirs(archive_path, exist_ok=True)
        
        moved_files = []
        for file_path, node in self.all_files.items():
            # Skip used files
            if node.is_used:
                continue
                
            # Skip safelist files unless force flag is set  
            if self._is_safelist_file(file_path) and not self.args.force:
                logger.info(f"Skipping safelist file: {node.rel_path}")
                continue
            
            # Create directory structure in archive
            rel_path = os.path.relpath(file_path, ROOT_DIR)
            archive_file = os.path.join(archive_path, rel_path)
            os.makedirs(os.path.dirname(archive_file), exist_ok=True)
            
            # Move file to archive
            try:
                shutil.copy2(file_path, archive_file)  # Copy first for safety
                os.unlink(file_path)  # Then delete original
                moved_files.append(rel_path)
                logger.info(f"Archived: {rel_path}")
            except Exception as e:
                logger.error(f"Failed to archive {rel_path}: {str(e)}")
        
        logger.info(f"Archived {len(moved_files)} unused files to {archive_path}")
        return moved_files
    
    def generate_visualization(self):
        """Generate a visual dependency graph if matplotlib is available"""
        try:
            import matplotlib.pyplot as plt
            import networkx as nx
            
            logger.info("Generating dependency visualizations...")
            
            # Create a directed graph
            G = nx.DiGraph()
            
            # Add nodes (files)
            for file_path in self.all_files:
                rel_path = os.path.relpath(file_path, ROOT_DIR)
                is_used = file_path in self.used_files
                is_entry = file_path in self.entry_points
                
                # Use different node types based on file characteristics
                if is_entry:
                    node_type = 'entry'
                elif not is_used:
                    node_type = 'unused'
                else:
                    node_type = 'used'
                    
                G.add_node(file_path, label=rel_path, type=node_type)
            
            # Add edges (import relationships)
            for file_path, imports in self.import_graph.items():
                for imported in imports:
                    G.add_edge(file_path, imported)
            
            # Save full dependency graph
            plt.figure(figsize=(20, 20))
            pos = nx.spring_layout(G, k=0.2, iterations=50)
            
            # Draw nodes with different colors
            node_colors = {
                'entry': 'red',
                'used': 'blue',
                'unused': 'gray'
            }
            
            for node_type, color in node_colors.items():
                nodelist = [n for n, data in G.nodes(data=True) if data.get('type') == node_type]
                nx.draw_networkx_nodes(G, pos, nodelist=nodelist, node_color=color, node_size=100, alpha=0.8)
            
            # Draw edges and labels
            nx.draw_networkx_edges(G, pos, width=0.5, alpha=0.5, arrows=True, arrowsize=10)
            
            # Only show labels for important nodes
            labels = {}
            for node, data in G.nodes(data=True):
                if data.get('type') in ['entry'] or G.degree(node) > 5:
                    module_name = os.path.splitext(data['label'])[0].replace(os.path.sep, '.')
                    labels[node] = module_name
            
            nx.draw_networkx_labels(G, pos, labels, font_size=8)
            
            plt.title("AthenaCore Import Dependencies")
            plt.axis('off')
            plt.savefig(os.path.join(REPORT_DIR, 'dependency_graph.png'), dpi=300)
            plt.close()
            
            logger.info(f"Visualization saved to {os.path.join(REPORT_DIR, 'dependency_graph.png')}")
            
        except ImportError as e:
            logger.warning(f"Visualization dependencies not available: {str(e)}")
            logger.warning("To generate visualizations, install matplotlib and networkx:")
            logger.warning("pip install matplotlib networkx")
    
    def print_summary(self):
        """Print a summary of the audit results"""
        elapsed_time = time.time() - self.start_time
        
        print("\n====== AthenaCore Codebase Audit Summary ======", flush=True)
        print(f"Project root: {ROOT_DIR}", flush=True)
        print(f"Time taken: {elapsed_time:.2f} seconds", flush=True)
        print(f"\nStatistics:", flush=True)
        print(f"  Total Python files: {self.stats['py_files']}", flush=True)
        print(f"  Used files: {self.stats['used_files']}", flush=True)
        print(f"  Unused files: {self.stats['unused_files']}", flush=True)
        print(f"  Entry points: {self.stats['entry_points']}", flush=True)
        print(f"  Import relationships: {self.stats['imports']}", flush=True)
        
        # Add special analysis for AthenaCore-specific components
        kb_files = self._count_special_files('src/core/kb_')
        doc_processor_files = self._count_special_files('src/core/document_processor')
        if kb_files > 0 or doc_processor_files > 0:
            print(f"\nAthenaCore Special Components:", flush=True)
            if kb_files > 0:
                print(f"  Knowledge Base files: {kb_files}", flush=True)
            if doc_processor_files > 0:
                print(f"  Document Processor files: {doc_processor_files}", flush=True)
        
        if self.stats['circular_imports'] > 0:
            print(f"  Circular imports detected: {self.stats['circular_imports']}", flush=True)
            print(f"  See {os.path.join(REPORT_DIR, 'circular_imports.txt')} for details", flush=True)
        
        if self.stats['error_files'] > 0:
            print(f"  Files with parsing errors: {self.stats['error_files']}", flush=True)
            
        print(f"\nReports saved to: {REPORT_DIR}", flush=True)
        
        if not self.args.dry_run and self.stats['unused_files'] > 0:
            print(f"\nUnused files archived to: {ARCHIVE_DIR}", flush=True)
        elif self.args.dry_run and self.stats['unused_files'] > 0:
            print(f"\nDry run: {self.stats['unused_files']} files would have been archived", flush=True)
            print(f"See {os.path.join(REPORT_DIR, 'files_to_archive.txt')} for details", flush=True)
    
    def _count_special_files(self, pattern):
        """Count files matching a specific pattern"""
        count = 0
        for file_path in self.all_files:
            rel_path = os.path.relpath(file_path, ROOT_DIR)
            if pattern in rel_path.replace(os.path.sep, '/'):
                count += 1
        return count
    
    def _check_deprecated_imports(self):
        """Check for deprecated imports in the codebase (e.g., src.login)"""
        logger.info("Checking for deprecated imports...")
        
        deprecated_patterns = {
            'src.login': 'src.models',  # Based on the memory about fixed imports
        }
        
        results = []
        count = 0
        
        # Open each Python file and search for the deprecated patterns
        for file_path, node in self.all_files.items():
            rel_path = os.path.relpath(file_path, ROOT_DIR)
            file_deprecated = []
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                    for old_pattern, new_pattern in deprecated_patterns.items():
                        if old_pattern in content:
                            count += 1
                            file_deprecated.append(f"Found deprecated import: '{old_pattern}', should use '{new_pattern}'")
            except Exception as e:
                logger.warning(f"Error checking deprecated imports in {rel_path}: {str(e)}")
            
            if file_deprecated:
                results.append({
                    'file': rel_path,
                    'issues': file_deprecated
                })
        
        # Save results to a report file
        if results:
            with open(os.path.join(REPORT_DIR, 'deprecated_imports.txt'), 'w') as f:
                f.write("=== Deprecated Imports Report ===\n\n")
                for item in results:
                    f.write(f"\n{item['file']}:\n")
                    for issue in item['issues']:
                        f.write(f"  - {issue}\n")
            
            logger.warning(f"Found {count} deprecated import references. See {os.path.join(REPORT_DIR, 'deprecated_imports.txt')}")
        else:
            logger.info("No deprecated imports found")
            
        return count
    
    def _analyze_kb_components(self):
        """Analyze Knowledge Base components specifically"""
        logger.info("Analyzing Knowledge Base components...")
        
        kb_components = []
        doc_processor_components = []
        
        # Find KB and document processor related files
        for file_path, node in self.all_files.items():
            rel_path = os.path.relpath(file_path, ROOT_DIR)
            rel_path_posix = rel_path.replace(os.path.sep, '/')
            
            if 'kb_' in rel_path_posix or 'knowledge_base' in rel_path_posix:
                kb_components.append({
                    'file': rel_path,
                    'used': node.is_used
                })
            
            if 'document_processor' in rel_path_posix:
                doc_processor_components.append({
                    'file': rel_path,
                    'used': node.is_used
                })
        
        # Generate a specialized KB report
        with open(os.path.join(REPORT_DIR, 'kb_components.txt'), 'w') as f:
            f.write("=== Knowledge Base Components ===\n\n")
            
            f.write("Knowledge Base Files:\n")
            if kb_components:
                for comp in sorted(kb_components, key=lambda x: x['file']):
                    status = "Used" if comp['used'] else "UNUSED"
                    f.write(f"  - {comp['file']} [{status}]\n")
            else:
                f.write("  No knowledge base files found\n")
            
            f.write("\nDocument Processor Files:\n")
            if doc_processor_components:
                for comp in sorted(doc_processor_components, key=lambda x: x['file']):
                    status = "Used" if comp['used'] else "UNUSED"
                    f.write(f"  - {comp['file']} [{status}]\n")
            else:
                f.write("  No document processor files found\n")
            
            # Add check for key files that should be present based on memories
            key_kb_files = [
                'src/core/kb_catalog.py',
                'src/core/document_processor.py'
            ]
            
            f.write("\nKey Component Status:\n")
            for key_file in key_kb_files:
                file_path = os.path.join(ROOT_DIR, key_file)
                if os.path.exists(file_path):
                    in_used = file_path in self.used_files
                    status = "Used" if in_used else "Found but UNUSED"
                    f.write(f"  - {key_file}: {status}\n")
                else:
                    f.write(f"  - {key_file}: MISSING\n")
            
        logger.info(f"Knowledge Base analysis saved to {os.path.join(REPORT_DIR, 'kb_components.txt')}")
        
        return len(kb_components) + len(doc_processor_components)


def main():
    """Main entry point for the script"""
    try:
        # Parse command line arguments
        parser = argparse.ArgumentParser(description='AthenaCore Codebase Audit Tool')
        
        # Basic options
        parser.add_argument('--dry-run', action='store_true', help='Generate reports without moving files')
        parser.add_argument('--visualize', action='store_true', help='Generate dependency graph visualization')
        parser.add_argument('--detailed', action='store_true', help='Generate detailed code metrics')
        parser.add_argument('--force', action='store_true', help='Move files even if in safelist')
        
        # AthenaCore specific options
        parser.add_argument('--kb-focus', action='store_true', help='Focus analysis on Knowledge Base components')
        parser.add_argument('--check-deprecated', action='store_true', help='Check for deprecated imports (like src.login)')
        parser.add_argument('--custom-entry', nargs='+', help='Specify additional entry point(s)')
        
        # Output options
        parser.add_argument('--verbose', '-v', action='store_true', help='Show verbose output during audit')
        parser.add_argument('--output', '-o', help='Custom output directory for reports')
        
        args = parser.parse_args()
        
        # Handle custom output directory
        if args.output:
            global REPORT_DIR
            REPORT_DIR = os.path.abspath(args.output)
            os.makedirs(REPORT_DIR, exist_ok=True)
            
        # Handle custom entry points
        if args.custom_entry:
            global ENTRY_POINTS
            ENTRY_POINTS.extend(args.custom_entry)
        
        # Display initial information
        print(f"\n====== AthenaCore Codebase Auditor ======", flush=True)
        print(f"Project root: {ROOT_DIR}", flush=True)
        print(f"Run mode: {'Dry run (reporting only)' if args.dry_run else 'Full audit with archiving'}", flush=True)
        print(f"Detailed metrics: {'Yes' if args.detailed else 'No'}", flush=True)
        print(f"Visualization: {'Yes' if args.visualize else 'No'}", flush=True)
        print(f"Force mode: {'Yes' if args.force else 'No'}", flush=True)
        print("\nStarting audit...", flush=True)
        
        # Initialize and run the auditor
        auditor = CodebaseAuditor(args)
        exit_code = auditor.run()
        
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nAudit cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nError running audit: {str(e)}")
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    import traceback
    main()
