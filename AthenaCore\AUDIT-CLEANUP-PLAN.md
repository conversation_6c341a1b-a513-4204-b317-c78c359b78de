# AthenaCore Audit-Based Cleanup Plan

Based on the latest audit results from `auditcodebase.py`, this document provides a detailed plan for removing unused files from the system.

## Audit Summary (Latest Run)
- **Total Python files**: 150
- **Used production files**: 120  
- **Used test files**: 11
- **Unused files**: 19
- **Entry points**: 4 (auditcodebase.py, main.py, start_athena.py, src\main.py)
- **Import relationships**: 265
- **Circular imports**: 1 (critical issue)

## Files Identified for Removal (19 total)

### Category 1: Backup/Compatibility Files (2 files) - REVIEW REQUIRED
```
backups\login\models.py
backups\login\views.py
```
**Risk Level**: Medium - These are compatibility layers with deprecation warnings, not true backups
**Action**: Review if still needed for backward compatibility

**Note**: `auditcodebase_backup.py` mentioned in audit report doesn't exist in main directory

### Category 2: Migration Scripts (6 files) - REVIEW REQUIRED
```
migrations\add_config_entries.py
migrations\add_configurations_table.py
migrations\v1_add_configurations_table.py
migrations\v2_add_tasks_table.py
migrations\v3_add_authentication_tables.py
migrations\v4_model_refactoring.py
```
**Risk Level**: Medium - May be needed for database schema management
**Action**: Review each migration to determine if still needed

### Category 3: Utility Scripts (9 files) - REVIEW REQUIRED
```
scripts\codebase_analyzer.py
scripts\identify_obsolete_files.py
scripts\manage_migrations.py
scripts\migrate_imports.py
scripts\prepare_for_removal.py
scripts\run_migrations.py
scripts\track_refactoring_progress.py
scripts\update_imports.py
scripts\verify_dependencies.py
```
**Risk Level**: Low-Medium - Development tools that may be useful
**Action**: Review individually, archive useful ones

### Category 4: Debug Components (1 file) - REVIEW REQUIRED
```
src\admin\debug_routes.py
```
**Risk Level**: Low - Debug functionality
**Action**: Remove if not needed in production

## Critical Issues to Address

### Circular Import (HIGH PRIORITY)
- **File**: `src\core\kb_context.py`
- **Issue**: Self-referencing circular import
- **Impact**: Can cause import failures and runtime issues
- **Action Required**: Investigate and fix immediately

## Execution Plan

### Phase 1: Immediate Safe Cleanup
**Timeline**: Today
**Files**: 3 backup files
**Commands**:
```bash
# Remove backup files
rm auditcodebase_backup.py
rm -rf backups/login/
```

### Phase 2: Fix Critical Issues
**Timeline**: This week (High Priority)
**Action**: Fix circular import in `src\core\kb_context.py`

### Phase 3: Script Review and Cleanup
**Timeline**: This week
**Action**: Review each utility script:
1. Determine current relevance
2. Archive useful maintenance tools
3. Remove obsolete scripts

### Phase 4: Migration Review
**Timeline**: This week
**Action**: Review migration scripts:
1. Check if migrations are complete
2. Archive historical migrations
3. Keep only active/future migrations

### Phase 5: Final Cleanup
**Timeline**: Next week
**Action**: Remove debug routes if not needed

## Validation Steps

After each phase:
1. Run the audit script again to verify changes
2. Test all entry points (main.py, start_athena.py, src\main.py)
3. Run existing tests to ensure no functionality is broken
4. Check logs for any import errors

## Commands for Safe Cleanup

### Remove Backup Files (Phase 1)
```bash
# Navigate to project root
cd AthenaCore

# Remove backup files
rm auditcodebase_backup.py
rm backups/login/models.py
rm backups/login/views.py
```

### Verify After Cleanup
```bash
# Run audit again to verify
python auditcodebase.py --dry-run

# Test entry points
python main.py --help
python start_athena.py --help
python src/main.py --help
```

## Notes

1. **Always run in dry-run mode first**: `python auditcodebase.py --dry-run`
2. **Create backups before removal**: The audit script already creates backups
3. **Test after each phase**: Ensure system stability
4. **Monitor logs**: Watch for any import errors after cleanup

## Success Metrics

- Reduced unused files from 19 to target of <5
- No circular imports
- All entry points functional
- All tests passing
- Clean audit report
