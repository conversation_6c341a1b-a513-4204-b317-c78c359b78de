# Model Context Protocol (MCP) Integration Guide

## Overview

AthenaAgent_Core now integrates with the Model Context Protocol (MCP) via the Smithery Registry, enabling access to a wide range of specialized AI models and capabilities through a standardized interface.

The MCP integration allows Athena to:

1. Discover available MCP servers through the Smithery Registry
2. Connect to MCP servers via WebSocket
3. Route chat conversations to MCP models
4. Expose MCP functionality through REST API endpoints

## Installation and Dependencies

To use the MCP integration, you need to install the required Python packages:

```bash
pip install smithery mcp
```

These dependencies are now included in the requirements.txt file, so you can also run:

```bash
pip install -r requirements.txt
```

## Quick Start Guide

### 1. Your Smithery API Key

Your Smithery API key has been configured as:
```
529c11c6-9189-4165-b77c-80bc84fda0c7
```

This key is now stored in your configuration and ready to use.

### 2. Testing MCP Connectivity

To test your MCP connectivity, run:
```bash
python test_mcp_connection.py list
```

This will list available MCP servers from the Smithery Registry.

### 3. Using MCP Models in Athena

To use an MCP model in your conversations, there are two ways to specify the model:

#### API Usage
```
model=mcp:smithery-ai/fetch
```

#### Athena GUI Usage
In the Athena GUI chat interface, you can specify the MCP model at the beginning of your message:

```
mcp:@smithery-ai/fetch What's the weather today?
```

The GUI will automatically extract the model specification and use it for processing your message.

Athena will automatically connect to the MCP server and route your messages appropriately.

## Configuration

### Setting Up MCP

To use MCP functionality, you need a Smithery API key. Configure it in one of two ways:

#### 1. Environment Variable

```bash
# Windows PowerShell
$env:SMITHERY_API_KEY="your-smithery-api-key-here"

# Linux/macOS
export SMITHERY_API_KEY="your-smithery-api-key-here"
```

#### 2. Configuration File

In your `config.json` file, add:

```json
{
  "smithery_api_key": "your-smithery-api-key-here",
  "enable_mcp": true,
  "mcp_registry_url": "https://registry.smithery.ai"
}
```

### Configuration Options

| Option | Description | Default |
|--------|-------------|--------|
| `smithery_api_key` | Your API key for Smithery Registry | `""` (empty string) |
| `enable_mcp` | Whether to enable MCP integration | `true` |
| `mcp_registry_url` | URL for the Smithery Registry API | `"https://registry.smithery.ai"` |

## Using MCP Models in Chat

To use an MCP server for chat, specify the model using one of these formats:
- `mcp:qualified_name` 
- `mcp:@qualified_name` 

Where `qualified_name` is the owner/repo identifier in the Smithery Registry.

### Examples

To use an MCP server named "smithery-ai/fetch":

```
model=mcp:smithery-ai/fetch
```

Or in the Athena GUI:

```
mcp:@smithery-ai/fetch Hello, can you help me?
```

This routing happens automatically when a model string with the `mcp:` prefix is detected.

## Server Status Tracking

The Athena Agent implements an efficient caching mechanism for tracking MCP server status without constant pinging:

### Status Cache System

- **Initial Status**: Servers start with "Unknown" status (blue indicator)
- **Cache Duration**: Server status is cached for 5 minutes by default
- **Manual Refresh**: Users can force a status refresh using the REFRESH button
- **Background Checking**: Status checks happen in separate threads to prevent UI freezing

### Status Display

- **Blue**: Unknown or refreshing status
- **Green**: Server is online and responding
- **Red**: Server is offline or unreachable

### Implementation Details

- Status is stored in a cache dictionary with server IDs as keys
- Each status entry contains the status text, style information, and timestamp
- Cached entries expire after the configured cache duration
- Status checks use background threads with proper thread-safe UI updates

This system ensures accurate server status information while minimizing network traffic and server load.

## MCP Server Verification and Troubleshooting

When attempting to connect to an MCP server, Athena will:

1. Verify the server exists by making an HTTP request to the Smithery Registry
2. Attempt a WebSocket connection for real-time communication
3. If WebSocket fails, attempt an HTTP fallback request

### Common Connection Issues

#### "MCP server failed to initialize check config" Error

If you get the error "MCP server failed to initialize check config", follow these steps:

1. **Install Dependencies**: Make sure you have installed the required packages:
   ```bash
   pip install smithery mcp
   ```

2. **Check Your Smithery API Key**: The API key must be correctly configured in one of these ways:
   - In `config.json` file:
     ```json
     {
       "smithery_api_key": "your-api-key-here",
       "enable_mcp": true
     }
     ```
   - As an environment variable: `SMITHERY_API_KEY=your-api-key-here`
   - Through the Athena GUI under MCP settings

3. **Verify Configuration Path**: Make sure your `config.json` file is in the expected location

4. **Restart AthenaCore**: After making configuration changes, restart the server

* **404 Server Not Found**: The specified MCP server doesn't exist or isn't accessible with your API key.
  - Ensure you're using the correct server name format: `mcp:@owner/model`
  - Visit the [Smithery Registry](https://registry.smithery.ai) to browse available servers
  - Verify your API key has access to the requested server

* **Authentication Errors**: Your Smithery API key may be invalid or expired
  - Check your `config.json` file to ensure the API key is correctly set
  - Regenerate your API key if necessary from the Smithery dashboard

### Finding Available MCP Servers

To find available MCP servers, you can:

1. Visit the [Smithery Registry](https://registry.smithery.ai) to browse public servers
2. Use the Smithery API to programmatically list available servers:
   ```python
   from src.mcp.smithery_client import SmitheryClient
   
   client = SmitheryClient(api_key="your_api_key")
   servers = client.list_servers()
   print("Available servers:")
   for server in servers:
       print(f"- mcp:@{server['owner']}/{server['name']}")
   ```

3. Check the Smithery documentation for recommended servers and models

## API Endpoints

The MCP integration adds several new API endpoints to AthenaCore:

### 1. List MCP Servers

**Endpoint:** `GET /api/v1/mcp/servers`

**Description:** Search for available MCP servers in the Smithery Registry.

**Parameters:**
- `q`: Search query (optional)
- `page`: Page number (default: 1)
- `pageSize`: Items per page (default: 10)

**Filtering Syntax:**
- Text search: "machine learning"
- Owner filter: owner:username
- Repository filter: repo:repository-name
- Deployment status: is:deployed
- Combined example: "owner:smithery-ai repo:fetch is:deployed machine learning"

**Response Example:**
```json
{
  "servers": [
    {
      "qualifiedName": "@smithery-ai/fetch",
      "displayName": "Fetch",
      "description": "A tool for retrieving web content",
      "homepage": "https://smithery.ai/fetch",
      "useCount": "1024",
      "isDeployed": true,
      "createdAt": "2025-01-15T12:00:00Z"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "pageSize": 10,
    "totalPages": 1,
    "totalCount": 1
  }
}
```

### 2. Get MCP Server Details

**Endpoint:** `GET /api/v1/mcp/servers/{qualifiedName}`

**Description:** Get detailed information about a specific MCP server.

**Response Example:**
```json
{
  "qualifiedName": "@smithery-ai/fetch",
  "displayName": "Fetch",
  "deploymentUrl": "https://server.smithery.ai/smithery-ai/fetch",
  "connections": [
    {
      "type": "ws",
      "configSchema": {
        "type": "object",
        "properties": {
          "temperature": {
            "type": "number",
            "default": 0.7
          }
        }
      }
    }
  ]
}
```

### 3. Create WebSocket URL

**Endpoint:** `POST /api/v1/mcp/connection/websocket-url`

**Description:** Generate a WebSocket URL for connecting to an MCP server.

**Request Body:**
```json
{
  "qualifiedName": "smithery-ai/fetch",
  "config": {
    "temperature": 0.8
  }
}
```

**Response Example:**
```json
{
  "url": "https://server.smithery.ai/smithery-ai/fetch/ws?config=eyJ0ZW1wZXJhdHVyZSI6MC44fQ==",
  "serverInfo": {
    "qualifiedName": "@smithery-ai/fetch",
    "displayName": "Fetch",
    "deploymentUrl": "https://server.smithery.ai/smithery-ai/fetch"
  }
}
```

### 4. Check Connection Status

**Endpoint:** `GET /api/v1/mcp/status`

**Description:** Check if the connection to the Smithery Registry is working.

All MCP API endpoints require authentication with an Athena API key. Include it in requests via the `X-API-Key` header:

```
X-API-Key: your-athena-api-key
```

## User-Specific API Keys

In addition to global configuration, Athena now supports user-specific API keys for MCP services. These keys are stored securely in the database and associated with each user account.

### Setting User API Keys

When you use a command to set an API key, it's now stored both in the global config and in your user profile:

```
athena set smithery_api_key YOUR_SMITHERY_API_KEY
athena set brave_api_key YOUR_BRAVE_API_KEY
athena set github_token YOUR_GITHUB_TOKEN
```

### API Endpoints for User Keys

Athena provides REST API endpoints to manage user-specific API keys:

**List Keys:** `GET /api/v1/mcp/api-keys`
**Add/Update Key:** `POST /api/v1/mcp/api-keys`
**Delete Key:** `DELETE /api/v1/mcp/api-keys/{key_id}`

These endpoints require authentication with a valid API key.

## MCP Configuration UI

The Athena Agent now features a sci-fi themed UI component for managing MCP API keys and exploring available MCP servers, inspired by the aesthetic of Solo Leveling.

### Accessing the MCP Configuration UI

To access the MCP Configuration interface:

1. Launch the Athena Agent application
2. Click on the **Settings** menu in the top menu bar
3. Select **MCP Server Configuration** from the dropdown menu

### Features

The MCP Configuration UI provides the following functionality through its sleek, futuristic interface:

#### 1. API Keys Management

The **API Keys** tab allows you to configure service-specific MCP API keys:

- **Smithery API Key**: Required for accessing the Smithery Registry and MCP servers
- **Brave Search API Key**: For enhanced web search capabilities
- **GitHub Token**: For integration with GitHub repositories

All keys are stored securely in your user profile and synchronized with the Athena database.

#### 2. Server Browser

The **Server Browser** tab provides a visual interface for discovering and connecting to available MCP servers:

- View a list of available MCP servers with descriptions and status indicators
- Search for specific servers by name or functionality
- Connect to servers directly from the interface
- Monitor connection status in real-time

### Testing Connections

The UI includes built-in functionality to test your MCP connections before saving:

1. Enter your API keys in the appropriate fields
2. Click the **TEST CONNECTION** button
3. The UI will display the connection status with visual indicators

This allows you to verify your configuration without leaving the Athena Agent interface.

## Server Templates

The MCP integration includes a template system that allows users to create, save, and manage MCP server templates. Templates provide a quick way to create new servers based on common patterns or previous successful implementations.

### System Templates

Athena comes with several built-in system templates that cover common use cases:

1. **Web Services** - Server with web search and content extraction tools
2. **Database** - Server for database access and querying
3. **System** - Server for system command execution and monitoring
4. **File System** - Server for file operations and document processing

System templates cannot be modified or deleted but can be used as a starting point for creating new servers or custom templates.

### User Templates

Users can create their own templates by:

1. Starting with a system template or from scratch
2. Modifying the server definition (tools, resources, dependencies)
3. Saving it as a new template

User templates are stored in the database and can be updated or deleted as needed.

### Using the Template System

The template system is available through both the API and the MCP Designer UI:

#### API Endpoints

- `GET /api/v1/mcp/templates` - List all available templates
- `GET /api/v1/mcp/templates/<template_id>` - Get a specific template
- `POST /api/v1/mcp/templates` - Create a new template
- `PUT /api/v1/mcp/templates/<template_id>` - Update an existing template
- `DELETE /api/v1/mcp/templates/<template_id>` - Delete a template

#### MCP Designer UI

The MCP Designer UI provides a visual interface for working with templates:

1. **Template Selection** - Choose from available system and user templates
2. **Template Editing** - Modify the server definition visually
3. **Template Management** - Save, update, or delete templates

Access the MCP Designer from the main application menu: **MCP > Server Designer**

### Database Schema

Templates are stored in the `mcp_server_templates` table with the following schema:

- `id` - Unique identifier
- `user_id` - User who created the template
- `name` - Template name
- `description` - Template description
- `server_definition` - JSON string of the server definition
- `created_at` - Creation timestamp
- `updated_at` - Last update timestamp

## Troubleshooting

### Common Issues

1. **"MCP system is not initialized"**: This error occurs when the MCP components haven't been properly initialized. Make sure:
   - You have installed the required `smithery` and `mcp` Python packages
   - Your Smithery API key is correctly configured
   - The MCP integration is enabled in your configuration

2. **Connection Errors**: If you're having trouble connecting to MCP servers:
   - Check your network connection
   - Verify that the MCP server is available by using `test_mcp_connection.py info <server-name>`
   - Ensure your Smithery API key has the necessary permissions

3. **GUI Integration Issues**: If the Athena GUI doesn't properly handle MCP models:
   - Make sure your message follows the format `mcp:@qualified-name Your message`
   - Check the Athena Core logs for more detailed error information
   - Restart both the Athena Core and Athena Agent components

### Testing the MCP Connection

Use the provided test script to verify your MCP connection:

```bash
# List available servers
python test_mcp_connection.py list

# Get info about a specific server
python test_mcp_connection.py info smithery-ai/fetch

# Test a connection to a server
python test_mcp_connection.py connect smithery-ai/fetch
```

## Sample Code

Here's a Python example for using MCP in your own applications:

```python
import smithery
import mcp
from mcp.client.websocket import websocket_client

# Create Smithery URL with server endpoint
url = smithery.create_smithery_url("wss://server.smithery.ai/@smithery-ai/server-sequential-thinking/ws", {}) + "&api_key=your-smithery-api-key"

async def main():
    # Connect to the server using websocket client
    async with websocket_client(url) as streams:
        async with mcp.ClientSession(*streams) as session:
            # List available tools
            tools_result = await session.list_tools()
            print(f"Available tools: {', '.join([t.name for t in tools_result])}")
            
            # Example: Call a tool
            # result = await session.call_tool("tool_name", {"param1": "value1"})
