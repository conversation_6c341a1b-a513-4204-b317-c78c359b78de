#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Temporary script to check database.db contents
"""

import sqlite3
import os

def check_database_db():
    # Check both database files
    db_paths = [
        "AthenaCore/database.db",
        "AthenaCore/instance/athena.db",
        "instance/athena.db"
    ]

    for db_path in db_paths:
        if not os.path.exists(db_path):
            print(f"Database file not found: {db_path}")
            continue

        print(f"\n{'='*60}")
        print(f"Checking database at: {db_path}")
        print(f"File size: {os.path.getsize(db_path)} bytes")
    
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # Get all tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()

            print(f"\nTables in {os.path.basename(db_path)} ({len(tables)} total):")
            for table in tables:
                table_name = table[0]
                print(f"  - {table_name}")

                # Get row count for each table
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"    Rows: {count}")

                # Show column info
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                print(f"    Columns: {', '.join([col[1] for col in columns])}")

                # Show sample data for small tables
                if count > 0 and count <= 5:
                    cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                    rows = cursor.fetchall()
                    if rows:
                        print(f"    Sample data: {rows[0]}")
                print()

            conn.close()

        except Exception as e:
            print(f"Error examining database {db_path}: {e}")

if __name__ == "__main__":
    check_database_db()
