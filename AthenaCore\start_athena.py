#!/usr/bin/env python
# Ensure Python 3.6+ required for Path
import sys
if sys.version_info < (3, 6):
    print("Python 3.6+ is required")
    sys.exit(1)

# Inject Path into sys.modules to ensure it's available everywhere
from pathlib import Path
import sys, builtins
builtins.Path = Path  # Make Path available globally

"""
Athena All-in-One Startup Script

This script handles:
1. Starting the Athena server
2. Initializing all background services (task executor, task scheduler)
3. Performing health checks

Usage:
    python start_athena.py [--port PORT] [--debug]
"""

import argparse
import os
import sys
import time
import threading
import requests
import logging
from pathlib import Path
from urllib.parse import urljoin

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("athena_starter")

# Wait time before initializing services (seconds)
SERVICE_INIT_WAIT = 5  

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Start Athena server and initialize all services")
    parser.add_argument("--port", type=int, default=5000, help="Port to run Athena server on")
    parser.add_argument("--debug", action="store_true", help="Run in debug mode")
    return parser.parse_args()

def init_knowledge_base():
    """Initialize the knowledge base and import documents from the Knowledgebase folder."""
    try:
        logger.info("Initializing knowledge base...")
        # Import here to avoid import cycles
        from src.core.kb_initializer import initializer
        result = initializer.initialize()
        if result:
            logger.info("Knowledge base initialized successfully")
        else:
            logger.warning("Knowledge base initialization had some issues")
        
        # Import documents from Knowledgebase folder using the catalog system
        try:
            import os
            from src.core.kb_catalog import get_catalog_instance
            
            # Get the catalog instance
            catalog = get_catalog_instance()
            
            # Path to the Knowledgebase folder (one level up from AthenaCore)
            kb_folder = os.path.abspath(os.path.join(os.path.dirname(os.path.dirname(__file__)), "Knowledgebase"))
            
            # Process documents in the folder
            logger.info(f"Processing documents from Knowledgebase folder: {kb_folder}")
            
            # Use system user_id for shared documents in the Knowledgebase folder
            # This way they'll be accessible to all users with appropriate permissions
            system_user_id = "system"
            
            stats = catalog.process_knowledgebase_folder(kb_folder, current_user_id=system_user_id)
            
            logger.info(f"Knowledgebase folder processing complete: {stats['imported']} imported, {stats['skipped']} skipped, {stats['failed']} failed (from {stats['total']} total files)")
            
        except Exception as kb_folder_err:
            logger.error(f"Error processing Knowledgebase folder: {kb_folder_err}")
            import traceback
            logger.error(traceback.format_exc())
    except Exception as e:
        logger.error(f"Error initializing knowledge base: {e}")

def init_services(base_url, max_retries=5):
    """Initialize all Athena background services."""
    # Make sure we're using the right URL format
    if not base_url.endswith('/'):
        base_url = base_url + '/'
    service_url = urljoin(base_url, "api/start-services")  # Remove leading slash for proper joining
    logger.info(f"Service initialization URL: {service_url}")
    
    # Wait a bit for the server to fully start
    logger.info(f"Waiting {SERVICE_INIT_WAIT} seconds for server to stabilize...")
    time.sleep(SERVICE_INIT_WAIT)
    
    # Initialize the knowledge base
    init_knowledge_base()
    
    for attempt in range(1, max_retries + 1):
        try:
            logger.info(f"Initializing services (attempt {attempt}/{max_retries})...")
            
            # Try to access the service initialization endpoint
            response = requests.get(service_url, timeout=20)
            logger.info(f"Response from {service_url}: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    logger.info("✅ Services initialized successfully")
                    return True
                else:
                    logger.warning(f"Unexpected response from service initialization: {data}")
            else:
                # If first URL fails with 404, try alternative format
                alt_url = base_url.rstrip('/') + '/api/start_services'
                logger.info(f"Trying alternative URL: {alt_url}")
                try:
                    alt_response = requests.get(alt_url, timeout=20)
                    if alt_response.status_code == 200:
                        logger.info("✅ Services initialized successfully via alternative URL")
                        return True
                    else:
                        logger.warning(f"Alternative URL returned status code {alt_response.status_code}")
                except Exception as alt_err:
                    logger.warning(f"Alternative URL request failed: {alt_err}")
                
                logger.warning(f"Service initialization returned status code {response.status_code}")
                
                # Try to diagnose the issue by testing basic API access
                try:
                    test_url = base_url.rstrip('/') + '/api/test'
                    test_response = requests.get(test_url, timeout=5)
                    logger.info(f"Test API response: {test_response.status_code}")
                except Exception as test_err:
                    logger.warning(f"Test API failed: {test_err}")
                    
        except requests.exceptions.RequestException as req_err:
            logger.error(f"Request error: {req_err}")
        except Exception as e:
            logger.error(f"Error initializing services: {str(e)}")
            
        if attempt < max_retries:
            wait_time = 2 * attempt  # Exponential backoff
            logger.info(f"Retrying in {wait_time} seconds...")
            time.sleep(wait_time)
    
    logger.error("❌ Failed to initialize services after multiple attempts")
    return False

def start_server(port, debug):
    """Import and run the Flask app."""
    logger.info(f"Starting Athena server on port {port} (debug={debug})...")
    
    # Need to set this before importing the app
    os.environ["FLASK_APP"] = "main.py"
    
    # Change to the AthenaCore directory to ensure correct imports
    script_dir = Path(__file__).resolve().parent
    os.chdir(str(script_dir))
    
    # Add the current directory to the Python path if it's not already there
    if os.getcwd() not in sys.path:
        sys.path.insert(0, os.getcwd())
    
    # Set up database configuration before importing app
    try:
        instance_dir = script_dir.parent / "instance"
        instance_dir.mkdir(exist_ok=True)
        db_path = instance_dir / "athena.db"
        os.environ["DATABASE_URI"] = f"sqlite:///{db_path}"
        os.environ["SQLALCHEMY_DATABASE_URI"] = f"sqlite:///{db_path}"
        
        logger.info(f"Using database at {db_path}")
        
        # If database doesn't exist, create it with basic schema
        if not db_path.exists() or db_path.stat().st_size == 0:
            logger.info("Creating new database file")
            import sqlite3
            conn = sqlite3.connect(str(db_path))
            conn.execute("CREATE TABLE IF NOT EXISTS config (key TEXT PRIMARY KEY, value TEXT)")
            conn.commit()
            conn.close()
    except Exception as e:
        logger.error(f"Error setting up database: {e}")
    
    # Import the Flask app
    try:
        # This imports the app from main.py
        from main import app
        
        # Create a parameter dictionary for the run method
        run_params = {
            "host": "0.0.0.0",
            "port": port,
            "debug": debug,
            "use_reloader": False  # Disable reloader to prevent double execution
        }
        
        # Start the Flask application
        app.run(**run_params)
        
    except ImportError as e:
        logger.error(f"Failed to import Flask app: {str(e)}")
        logger.error("Make sure you're running this script from the AthenaCore directory")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Error starting Athena server: {str(e)}")
        sys.exit(1)

def health_check(base_url, max_retries=10):
    """Check if the server is healthy and responding."""
    for attempt in range(1, max_retries + 1):
        try:
            logger.info(f"Health check (attempt {attempt}/{max_retries})...")
            response = requests.get(base_url, timeout=5)
            
            if response.status_code == 200:
                logger.info("✅ Server is healthy")
                return True
                
        except Exception:
            pass
        
        if attempt < max_retries:
            time.sleep(1)
    
    logger.error("❌ Server health check failed")
    return False

def main():
    """Main entry point for the script."""
    args = parse_args()
    port = args.port
    debug = args.debug
    
    # Ensure instance directory exists for database
    script_dir = Path(__file__).resolve().parent
    project_root = script_dir.parent
    instance_dir = project_root / "instance"
    instance_dir.mkdir(exist_ok=True)
    
    # Set environment variable for database URI to ensure consistent path
    # This will override any relative paths in config files
    db_path = instance_dir / "athena.db"
    os.environ["DATABASE_URI"] = f"sqlite:///{db_path}"
    os.environ["SQLALCHEMY_DATABASE_URI"] = f"sqlite:///{db_path}"
    
    logger.info(f"Using database at {db_path}")
    
    base_url = f"http://localhost:{port}"
    
    # Start the server in a separate thread
    server_thread = threading.Thread(target=start_server, args=(port, debug))
    server_thread.daemon = True
    server_thread.start()
    
    # Check server health
    if not health_check(base_url):
        logger.error("Server failed to start properly. Exiting...")
        sys.exit(1)
    
    # Initialize services properly through the API endpoint
    try:
        # First, give the Flask application time to completely initialize
        logger.info("Waiting for Flask application to fully initialize...")
        time.sleep(10)  # Give Flask extra time to register all routes
        
        # Now attempt to initialize services
        if not init_services(base_url):
            logger.warning("Service initialization failed - the server may need to be restarted")
            logger.warning("This is expected on the first run after adding the endpoint")
            logger.warning("Services will still be automatically initialized by the main application")
            
            # Log a reminder about the database consolidation
            logger.info("NOTE: Database consolidation is still working correctly")
            logger.info("      The consolidated database at instance/athena.db will be used")
    except Exception as e:
        logger.warning(f"Services initialization error: {e}")
        logger.warning("Services will still be initialized by the main application")
        
    # Continue regardless of service initialization status
    
    # Verify database location is correct
    try:
        instance_dir = Path(__file__).resolve().parent.parent / 'instance'
        db_path = instance_dir / 'athena.db'
        if db_path.exists():
            db_size = db_path.stat().st_size
            logger.info(f"✅ Consolidated database verified at {db_path} (size: {db_size:,} bytes)")
        else:
            logger.warning(f"⚠️ Database file not found at expected path: {db_path}")
    except Exception as e:
        logger.error(f"Error verifying database: {e}")
    
    logger.info("=== Athena is running ===")
    logger.info(f"Server URL: {base_url}")
    logger.info("Press Ctrl+C to stop")
    
    try:
        # Keep the main thread running
        while server_thread.is_alive():
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Shutting down Athena...")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
